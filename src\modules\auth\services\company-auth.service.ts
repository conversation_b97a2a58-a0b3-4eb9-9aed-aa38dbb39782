import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompanyAccountRepository } from '../repositories/company-account.repository';
import { UserRepository } from '../repositories/user.repository';
import { PermissionRepository } from '../repositories/permission.repository';
import {
  CompanyRegisterDto,
  CompanyLoginDto,
  VerifyEmailDto,
  ResendOtpDto,
} from '../dto';
import { CompanyStatus } from '../enum/company-status.enum';
import { UserStatus } from '../enum/user-status.enum';
import { EncryptionService } from '@shared/services/encryption.service';
import { EmailService } from '@shared/services/email';
import { RedisService } from '@shared/services/redis.service';
import { RecaptchaService } from '@shared/services/recaptcha.service';
import { AppException, ErrorCode } from '@/common';
import { JwtUtilService, TokenType } from '../guards/jwt.util';
import { AUTH_ERROR_CODE } from '../errors/auth-error.code';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import {
  CompanyLoginResponseDto,
  CompanyRegisterResponseDto,
  CompanyResponseDto,
  ResendOtpResponseDto,
  VerifyEmailResponseDto,
} from '../dto/company-response.dto';
import { Transactional } from 'typeorm-transactional';
import { ConfigService } from '@/config/config.service';
import { generateRandomWithDate } from '@/shared/utils/random/generate-random-with-date';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { UserRole } from '../entities/user-role.entity';

/**
 * Service xử lý authentication cho tài khoản công ty
 */
@Injectable()
export class CompanyAuthService {
  private readonly logger = new Logger(CompanyAuthService.name);
  private readonly registrationPrefix = 'company_registration:';

  constructor(
    private readonly companyRepository: CompanyAccountRepository,
    private readonly userRepository: UserRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly encryptionService: EncryptionService,
    private readonly jwtService: JwtUtilService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    private readonly redisService: RedisService,
    private readonly recaptchaService: RecaptchaService,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionEntityRepository: Repository<Permission>,
    @InjectRepository(RolePermission)
    private readonly rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
  ) {}

  /**
   * Che một phần email để hiển thị
   * @param email Email cần che
   * @returns Email đã được che một phần
   */
  private maskEmail(email: string | null): string {
    if (!email) return '';

    const [username, domain] = email.split('@');

    if (!username || !domain) return email;

    // Nếu username có 1-2 ký tự, chỉ hiển thị ký tự đầu tiên
    if (username.length <= 2) {
      return `${username.charAt(0)}***@${domain}`;
    }

    // Nếu username có 3-5 ký tự, hiển thị ký tự đầu tiên và cuối cùng
    if (username.length <= 5) {
      return `${username.charAt(0)}***${username.charAt(username.length - 1)}@${domain}`;
    }

    // Nếu username dài hơn 5 ký tự, hiển thị 2 ký tự đầu và 2 ký tự cuối
    return `${username.substring(0, 2)}***${username.substring(username.length - 2)}@${domain}`;
  }

  /**
   * Đăng ký tài khoản công ty mới
   * @param registerDto Thông tin đăng ký
   * @returns Thông tin đăng ký thành công
   */
  @Transactional()
  async register(
    registerDto: CompanyRegisterDto,
  ): Promise<ApiResponseDto<CompanyRegisterResponseDto>> {
    // Xác thực reCAPTCHA token
    try {
      if (registerDto.recaptchaToken) {
        // Bỏ comment dòng dưới đây khi triển khai thực tế
        // const recaptchaResponse = await this.recaptchaService.verifyRecaptcha(registerDto.recaptchaToken);
        //
        // if (!recaptchaResponse.success) {
        //   throw new AppException(ErrorCode.RECAPTCHA_VERIFICATION_FAILED);
        // }
      }
    } catch (error) {
      throw new AppException(
        AUTH_ERROR_CODE.TOO_MANY_REQUESTS,
        'Xác thực reCAPTCHA thất bại',
      );
    }

    // Kiểm tra email công ty đã tồn tại chưa
    const existingCompanyEmail = await this.companyRepository.findByEmail(
      registerDto.companyEmail,
    );
    if (existingCompanyEmail) {
      throw new AppException(
        AUTH_ERROR_CODE.EMAIL_ALREADY_EXISTS,
        'Email công ty đã được sử dụng bởi một tài khoản khác',
      );
    }

    // Kiểm tra email của admin đã tồn tại chưa
    const existingUserEmail = await this.userRepository.findByEmail(
      registerDto.companyEmail, // Sử dụng email công ty cho tài khoản admin
    );

    if (existingUserEmail) {
      throw new AppException(
        AUTH_ERROR_CODE.EMAIL_ALREADY_EXISTS,
        'Email đã được sử dụng bởi một tài khoản người dùng khác',
      );
    }

    // Tạo username từ email và 3 số ngẫu nhiên
    const emailParts = registerDto.companyEmail.split('@');
    const usernameBase = emailParts[0].toLowerCase().replace(/[^a-z0-9]/g, '');
    const randomNumbers = Math.floor(Math.random() * 900) + 100; // Số ngẫu nhiên từ 100-999
    let username = `${usernameBase}${randomNumbers}`;

    // Kiểm tra username đã tồn tại chưa
    let existingUsername = await this.userRepository.findByUsername(username);

    // Nếu username đã tồn tại, thử lại với số ngẫu nhiên khác
    let attempts = 0;
    while (existingUsername && attempts < 5) {
      const newRandomNumbers = Math.floor(Math.random() * 900) + 100;
      username = `${usernameBase}${newRandomNumbers}`;
      existingUsername = await this.userRepository.findByUsername(username);
      attempts++;
    }

    // Nếu vẫn không tìm được username phù hợp sau 5 lần thử
    if (existingUsername) {
      throw new AppException(
        AUTH_ERROR_CODE.USERNAME_ALREADY_EXISTS,
        'Không thể tạo tên đăng nhập duy nhất, vui lòng thử lại',
      );
    }

    // Log username đã tạo để debug
    this.logger.log(`Đã tạo username tự động: ${username}`);

    // Mã hóa mật khẩu
    const hashedPassword = this.encryptionService.hashPassword(
      registerDto.password,
    );

    // Tạo ID cho công ty mới
    const companyId = generateRandomWithDate();
    const now = Date.now();

    // Tạo tài khoản công ty mới với trạng thái INACTIVE
    const newCompany = await this.companyRepository.create({
      id: companyId,
      companyName: registerDto.companyName,
      companyEmail: registerDto.companyEmail,
      // phoneNumber: registerDto.phoneNumber,
      status: CompanyStatus.INACTIVE, // Chưa kích hoạt cho đến khi xác thực email
      createdAt: now,
    });

    // Tạo tài khoản người dùng quản trị với trạng thái INACTIVE
    const adminUser = await this.userRepository.create({
      username: username, // Sử dụng username đã tạo tự động
      email: registerDto.companyEmail, // Sử dụng email công ty cho tài khoản admin
      password: hashedPassword,
      fullName: registerDto.companyName, // Sử dụng tên công ty làm tên người dùng
      status: UserStatus.INACTIVE, // Chưa kích hoạt cho đến khi xác thực email
      createdAt: now,
      tenantId: companyId, // Gán tenantId là ID của công ty
    });

    // Tạo mã OTP 6 chữ số
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // Tạo token OTP
    const { token: otpToken, expiresInSeconds } = this.jwtService.generateToken(
      {
        id: newCompany.id,
        sub: adminUser.id, // Sử dụng ID người dùng làm subject
        tenantId: newCompany.id,
        type: 'COMPANY_ADMIN',
        typeToken: TokenType.VERIFY,
      },
      TokenType.VERIFY,
      '30m',
    );

    // Lưu thông tin OTP vào Redis để sử dụng sau này
    const verificationData = {
      companyId: newCompany.id,
      userId: adminUser.id,
      email: newCompany.companyEmail,
      phoneNumber: newCompany.phoneNumber,
      otp,
      createdAt: now,
    };

    const redisKey = `${this.registrationPrefix}${otpToken}`;

    // Log để debug
    this.logger.log(
      `Attempting to store verification data in Redis with key: ${redisKey}`,
    );
    this.logger.log(`OTP: ${otp}`);
    this.logger.log(`Token: ${otpToken}`);

    try {
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(verificationData),
        expiresInSeconds,
      );

      // Kiểm tra xem dữ liệu đã được lưu chưa
      const storedData = await this.redisService.get(redisKey);
      this.logger.log(
        `Verification data stored in Redis: ${storedData ? 'Success' : 'Failed'}`,
      );

      this.logger.debug(
        `Verification data stored in Redis with key: ${redisKey}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store verification data in Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.REGISTRATION_FAILED,
        'Không thể lưu thông tin xác thực',
      );
    }

    // Che một phần email
    const maskedEmail = this.maskEmail(newCompany.companyEmail);

    // Gửi email chứa OTP cho người dùng
    try {
      // Gửi email xác thực
      if (newCompany.companyEmail && newCompany.companyName) {
        await this.emailService.sendVerificationEmail(
          newCompany.companyEmail,
          otp,
          newCompany.companyName,
        );
      } else {
        this.logger.error(
          'Missing company email or name for verification email',
        );
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thông tin công ty không đầy đủ để gửi email xác thực',
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${newCompany.companyEmail}: ${error.message}`,
        error,
      );
      // Không throw lỗi ở đây để không ảnh hưởng đến luồng đăng ký
    }

    // Tính toán thời điểm hết hạn thực tế (timestamp)
    const expiresAt = Date.now() + expiresInSeconds * 1000;

    // Trả về thông tin đăng ký
    const response: CompanyRegisterResponseDto = {
      message:
        'Mã OTP đã được gửi đến email. Vui lòng kiểm tra email để xác thực tài khoản.',
      otpToken,
      expiresAt,
      maskedEmail,
    };

    // Trong môi trường development, trả về cả OTP để tiện test
    if (this.configService.isDevelopment()) {
      response.otp = otp;
    }

    return ApiResponseDto.created(response);
  }

  /**
   * Xác thực email bằng OTP
   * @param verifyEmailDto Thông tin xác thực email
   * @returns Thông tin xác thực thành công
   */
  async verifyEmail(
    verifyEmailDto: VerifyEmailDto,
  ): Promise<ApiResponseDto<VerifyEmailResponseDto>> {
    const { token, otp } = verifyEmailDto;

    try {
      // Xác thực token
      this.jwtService.verifyTokenWithType(token, TokenType.VERIFY);
    } catch (error) {
      this.logger.error(`Invalid verification token: ${error.message}`);
      throw new AppException(
        AUTH_ERROR_CODE.TOKEN_INVALID_OR_EXPIRED,
        'Token xác thực không hợp lệ hoặc đã hết hạn',
      );
    }

    // Chuẩn hóa token để đảm bảo khớp với cách lưu trữ
    // Loại bỏ khoảng trắng đầu/cuối nếu có
    const normalizedToken = token.trim();

    // Lấy thông tin xác thực từ Redis
    const redisKey = `${this.registrationPrefix}${normalizedToken}`;

    // Log để debug
    this.logger.log(
      `Attempting to retrieve verification data from Redis with key: ${redisKey}`,
    );
    this.logger.log(`Original Token: ${token}`);
    this.logger.log(`Normalized Token: ${normalizedToken}`);
    this.logger.log(`OTP: ${otp}`);

    // Kiểm tra xem key có tồn tại không
    const exists = await this.redisService.exists(redisKey);
    this.logger.log(`Redis key exists: ${exists ? 'Yes' : 'No'}`);

    const verificationDataStr = await this.redisService.get(redisKey);
    this.logger.log(
      `Verification data retrieved from Redis: ${verificationDataStr || 'null'}`,
    );

    if (!verificationDataStr) {
      // Không thể lấy danh sách keys vì RedisService không có phương thức getClient()
      this.logger.error(`Verification data not found for key: ${redisKey}`);

      // Thử một cách khác để kiểm tra: Tạo một key test và kiểm tra xem Redis có hoạt động không
      const testKey = `redis_test_${Date.now()}`;
      try {
        await this.redisService.setWithExpiry(testKey, 'test', 10);
        const testValue = await this.redisService.get(testKey);
        this.logger.log(`Redis test: ${testValue ? 'Success' : 'Failed'}`);
        await this.redisService.del(testKey);
      } catch (e) {
        this.logger.error(`Redis test failed: ${e.message}`);
      }

      throw new AppException(
        AUTH_ERROR_CODE.VERIFICATION_CODE_INVALID,
        'Mã xác thực không hợp lệ hoặc đã hết hạn',
      );
    }

    // Parse dữ liệu xác thực
    let verificationData: {
      companyId: number;
      userId: number;
      email: string;
      phoneNumber?: string;
      otp: string;
      createdAt: number;
    };

    try {
      verificationData = JSON.parse(verificationDataStr);
    } catch (error) {
      this.logger.error(`Failed to parse verification data: ${error.message}`);
      throw new AppException(
        AUTH_ERROR_CODE.VERIFICATION_FAILED,
        'Lỗi xử lý dữ liệu xác thực',
      );
    }

    // Kiểm tra OTP
    if (verificationData.otp !== otp) {
      throw new AppException(
        AUTH_ERROR_CODE.VERIFICATION_CODE_INVALID,
        'Mã OTP không chính xác',
      );
    }

    // Tìm công ty theo ID
    const company = await this.companyRepository.findById(
      Number(verificationData.companyId),
    );
    if (!company) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Không tìm thấy tài khoản công ty',
      );
    }

    // Tìm người dùng theo ID
    const user = await this.userRepository.findById(
      Number(verificationData.userId),
    );
    if (!user) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Không tìm thấy tài khoản người dùng',
      );
    }

    if (!company.companyEmail) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Email công ty không hợp lệ',
      );
    }

    // Tạo subdomain từ email công ty (phần trước @)
    const subdomain = company.companyEmail.split('@')[0];
    const now = Date.now();

    // Cập nhật trạng thái và subdomain của công ty
    await this.companyRepository.update(company.id, {
      status: CompanyStatus.ACTIVE,
      subdomain,
      updatedAt: now,
    });

    // Cập nhật trạng thái của người dùng
    await this.userRepository.update(user.id, {
      status: UserStatus.ACTIVE,
    });

    // Xóa dữ liệu xác thực khỏi Redis
    await this.redisService.del(redisKey);

    // Gán quyền cho user admin
    await this.assignAdminPermissions(user.id, company.id);

    // Lấy danh sách quyền của người dùng
    const permissions = await this.permissionRepository.getUserPermissions(
      user.id,
    );

    // Tạo JWT token cho đăng nhập tự động
    const payload = {
      id: user.id, // Sử dụng ID người dùng
      sub: user.id, // Sử dụng ID người dùng làm subject
      email: user.email,
      name: user.fullName || company.companyName,
      typeToken: TokenType.ACCESS,
      tenantId: company.id, // Vẫn sử dụng ID công ty làm tenantId
      domain: company.subdomain,
      type: 'COMPANY_ADMIN' as const,
      permissions: permissions, // Thêm danh sách quyền vào token
    };

    // Tạo token và lấy thông tin token
    const { token: accessToken } = this.jwtService.generateToken(
      payload,
      TokenType.ACCESS,
      '24h',
    );

    // Chuyển đổi dữ liệu entity thành DTO
    const companyResponseDto = new CompanyResponseDto(company as any);

    const response: VerifyEmailResponseDto = {
      message:
        'Xác thực email thành công. Tài khoản của bạn đã được kích hoạt.',
      accessToken,
      company: companyResponseDto,
      permissions: permissions, // Thêm danh sách quyền vào phản hồi
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Gửi lại mã OTP xác thực
   * @param resendOtpDto Thông tin yêu cầu gửi lại OTP
   * @returns Thông tin gửi lại OTP thành công
   */
  async resendOtp(
    resendOtpDto: ResendOtpDto,
  ): Promise<ApiResponseDto<ResendOtpResponseDto>> {
    const { token } = resendOtpDto;

    try {
      // Xác thực token
      this.jwtService.verifyTokenWithType(token, TokenType.VERIFY);
    } catch (error) {
      this.logger.error(`Invalid verification token: ${error.message}`);
      throw new AppException(
        AUTH_ERROR_CODE.TOKEN_INVALID_OR_EXPIRED,
        'Token xác thực không hợp lệ hoặc đã hết hạn',
      );
    }

    // Lấy thông tin xác thực từ Redis
    const redisKey = `${this.registrationPrefix}${token}`;
    const verificationDataStr = await this.redisService.get(redisKey);

    if (!verificationDataStr) {
      throw new AppException(
        AUTH_ERROR_CODE.VERIFICATION_CODE_INVALID,
        'Mã xác thực không tồn tại hoặc đã hết hạn',
      );
    }

    // Parse dữ liệu xác thực
    let verificationData: {
      companyId: number;
      userId: number;
      email: string;
      phoneNumber?: string;
      otp: string;
      createdAt: number;
    };

    try {
      verificationData = JSON.parse(verificationDataStr);
    } catch (error) {
      this.logger.error(`Failed to parse verification data: ${error.message}`);
      throw new AppException(
        AUTH_ERROR_CODE.VERIFICATION_FAILED,
        'Lỗi xử lý dữ liệu xác thực',
      );
    }

    // Tìm công ty theo ID
    const company = await this.companyRepository.findById(
      Number(verificationData.companyId),
    );
    if (!company) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Không tìm thấy tài khoản công ty',
      );
    }

    // Tìm người dùng theo ID
    const user = await this.userRepository.findById(
      Number(verificationData.userId),
    );
    if (!user) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Không tìm thấy tài khoản người dùng',
      );
    }

    // Kiểm tra trạng thái tài khoản công ty
    if (company.status === CompanyStatus.ACTIVE) {
      throw new AppException(
        AUTH_ERROR_CODE.ACCOUNT_ALREADY_VERIFIED,
        'Tài khoản đã được xác thực trước đó',
      );
    }

    // Kiểm tra trạng thái tài khoản người dùng
    if (user.status === UserStatus.ACTIVE) {
      throw new AppException(
        AUTH_ERROR_CODE.ACCOUNT_ALREADY_VERIFIED,
        'Tài khoản người dùng đã được xác thực trước đó',
      );
    }

    // Tạo mã OTP 6 chữ số mới
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // Cập nhật thông tin OTP trong Redis
    verificationData.otp = otp;
    verificationData.createdAt = Date.now();

    // Lấy thời gian hết hạn của token hiện tại
    const tokenInfo = this.jwtService.decodeToken(token);
    const expiresInSeconds = Math.max(
      0,
      Math.floor(tokenInfo.exp - Date.now() / 1000),
    );

    if (expiresInSeconds <= 0) {
      throw new AppException(
        AUTH_ERROR_CODE.TOKEN_INVALID_OR_EXPIRED,
        'Token xác thực đã hết hạn',
      );
    }

    // Lưu lại thông tin cập nhật vào Redis
    try {
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(verificationData),
        expiresInSeconds,
      );
      this.logger.debug(
        `Updated verification data stored in Redis with key: ${redisKey}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update verification data in Redis: ${error.message}`,
        error,
      );
      throw new AppException(
        AUTH_ERROR_CODE.REGISTRATION_FAILED,
        'Không thể cập nhật thông tin xác thực',
      );
    }

    // Che một phần email
    const maskedEmail = this.maskEmail(company.companyEmail);

    // Gửi email chứa OTP mới cho người dùng
    try {
      // Gửi email xác thực
      if (company.companyEmail && company.companyName) {
        await this.emailService.sendVerificationEmail(
          company.companyEmail,
          otp,
          company.companyName,
        );
      } else {
        this.logger.error(
          'Missing company email or name for verification email',
        );
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thông tin công ty không đầy đủ để gửi email xác thực',
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${company.companyEmail}: ${error.message}`,
        error,
      );
      // Không throw lỗi ở đây để không ảnh hưởng đến luồng gửi lại OTP
    }

    // Tính toán thời điểm hết hạn thực tế (timestamp)
    const expiresAt = Date.now() + expiresInSeconds * 1000;

    // Trả về thông tin gửi lại OTP
    const response: ResendOtpResponseDto = {
      message: 'Mã OTP mới đã được gửi đến email của bạn.',
      otpToken: token, // Sử dụng lại token hiện tại
      expiresAt,
      maskedEmail,
    };

    // Trong môi trường development, trả về cả OTP để tiện test
    if (this.configService.isDevelopment()) {
      response.otp = otp;
    }

    return ApiResponseDto.success(response);
  }

  /**
   * Gán tất cả quyền cho user admin
   * @param userId ID của user admin
   * @param tenantId ID của công ty
   */
  private async assignAdminPermissions(
    userId: number,
    tenantId: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Assigning admin permissions for user ${userId} in tenant ${tenantId}`,
      );

      // Tạo role Admin nếu chưa tồn tại
      let adminRole = await this.roleRepository.findOne({
        where: {
          name: 'Admin',
          tenantId: tenantId,
        },
      });

      if (!adminRole) {
        // Tạo role Admin mới
        const now = Date.now();
        adminRole = await this.roleRepository.save({
          name: 'Admin',
          description: 'Quản trị viên với tất cả quyền',
          type: 'ADMIN',
          createdAt: now,
          tenantId: tenantId,
        });
        this.logger.log(`Created new Admin role with ID ${adminRole.id}`);
      }

      // Lấy tất cả các quyền
      const allPermissions = await this.permissionEntityRepository.find();
      this.logger.log(`Found ${allPermissions.length} permissions to assign`);

      // Gán tất cả quyền cho role Admin
      const now = Date.now();
      const rolePermissions = allPermissions.map((permission: Permission) => ({
        roleId: adminRole.id,
        permissionId: permission.id,
        createdAt: now,
        tenantId: tenantId,
      }));

      // Xóa các quyền cũ của role (nếu có)
      await this.rolePermissionRepository.delete({ roleId: adminRole.id });

      // Lưu các quyền mới
      if (rolePermissions.length > 0) {
        await this.rolePermissionRepository.save(rolePermissions);
        this.logger.log(
          `Assigned ${rolePermissions.length} permissions to Admin role`,
        );
      }

      // Gán role Admin cho user
      // Xóa các role cũ của user (nếu có)
      await this.userRoleRepository.delete({ userId: userId });

      // Gán role mới
      await this.userRoleRepository.save({
        userId: userId,
        roleId: adminRole.id,
        createdAt: now,
        tenantId: tenantId,
      });

      this.logger.log(`Successfully assigned Admin role to user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Error assigning admin permissions: ${error.message}`,
        error.stack,
      );
      // Không throw lỗi để không ảnh hưởng đến quá trình xác thực email
    }
  }
}
