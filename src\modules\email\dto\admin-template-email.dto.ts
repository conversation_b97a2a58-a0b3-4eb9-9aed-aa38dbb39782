import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsObject,
  IsNumber,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho việc tạo mới template email
 */
export class CreateAdminTemplateEmailDto {
  @ApiProperty({
    description: 'Tên mẫu email',
    example: 'Thông báo đăng ký thành công',
  })
  @IsNotEmpty({ message: 'Tên mẫu không được để trống' })
  @IsString({ message: 'Tên mẫu phải là chuỗi' })
  @MaxLength(100, { message: 'Tên mẫu không được vượt quá 100 ký tự' })
  name: string;

  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Xác nhận đăng ký tài khoản thành công',
  })
  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' })
  subject: string;

  @ApiProperty({
    description: 'Danh mục email',
    example: 'user_registration',
  })
  @IsNotEmpty({ message: 'Danh mục không được để trống' })
  @IsString({ message: 'Danh mục phải là chuỗi' })
  @MaxLength(100, { message: 'Danh mục không được vượt quá 100 ký tự' })
  category: string;

  @ApiProperty({
    description: 'Nội dung email (HTML)',
    example: '<p>Xin chào {{name}},</p><p>Cảm ơn bạn đã đăng ký tài khoản.</p>',
  })
  @IsNotEmpty({ message: 'Nội dung không được để trống' })
  @IsString({ message: 'Nội dung phải là chuỗi' })
  content: string;

  @ApiProperty({
    description: 'Danh sách các placeholder',
    example: { name: 'Tên người dùng', email: 'Email người dùng' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Placeholders phải là đối tượng' })
  placeholders?: Record<string, string>;

  @ApiProperty({
    description: 'ID của người tạo',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người tạo phải là số' })
  @Type(() => Number)
  createdBy?: number;
}

/**
 * DTO cho việc cập nhật template email
 */
export class UpdateAdminTemplateEmailDto {
  @ApiProperty({
    description: 'Tên mẫu email',
    example: 'Thông báo đăng ký thành công',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên mẫu phải là chuỗi' })
  @MaxLength(100, { message: 'Tên mẫu không được vượt quá 100 ký tự' })
  name?: string;

  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Xác nhận đăng ký tài khoản thành công',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' })
  subject?: string;

  @ApiProperty({
    description: 'Danh mục email',
    example: 'user_registration',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Danh mục phải là chuỗi' })
  @MaxLength(100, { message: 'Danh mục không được vượt quá 100 ký tự' })
  category?: string;

  @ApiProperty({
    description: 'Nội dung email (HTML)',
    example: '<p>Xin chào {{name}},</p><p>Cảm ơn bạn đã đăng ký tài khoản.</p>',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nội dung phải là chuỗi' })
  content?: string;

  @ApiProperty({
    description: 'Danh sách các placeholder',
    example: { name: 'Tên người dùng', email: 'Email người dùng' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Placeholders phải là đối tượng' })
  placeholders?: Record<string, string>;

  @ApiProperty({
    description: 'ID của người cập nhật',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người cập nhật phải là số' })
  @Type(() => Number)
  updatedBy?: number;
}

/**
 * DTO cho hiển thị danh sách template email (không bao gồm nội dung)
 */
@Exclude()
export class AdminTemplateEmailListDto {
  @Expose()
  @ApiProperty({
    description: 'ID của template',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tên mẫu email',
    example: 'Thông báo đăng ký thành công',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Xác nhận đăng ký tài khoản thành công',
  })
  subject: string;

  @Expose()
  @ApiProperty({
    description: 'Danh mục email',
    example: 'user_registration',
  })
  category: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1672531200000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1672531200000,
  })
  updatedAt: number;

  constructor(partial: Partial<AdminTemplateEmailListDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho việc gửi email với template
 */
export class SendTemplateEmailDto {
  @ApiProperty({
    description: 'Danh mục của template email',
    example: 'user_registration',
  })
  @IsNotEmpty({ message: 'Danh mục không được để trống' })
  @IsString({ message: 'Danh mục phải là chuỗi' })
  category: string;

  @ApiProperty({
    description: 'Email người nhận',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email người nhận không được để trống' })
  @IsString({ message: 'Email người nhận phải là chuỗi' })
  to: string;

  @ApiProperty({
    description: 'Dữ liệu để thay thế các placeholder trong template',
    example: { name: 'Nguyễn Văn A', email: '<EMAIL>' },
  })
  @IsNotEmpty({ message: 'Dữ liệu không được để trống' })
  @IsObject({ message: 'Dữ liệu phải là đối tượng' })
  data: Record<string, any>;
}
