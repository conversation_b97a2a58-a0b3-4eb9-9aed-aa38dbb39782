import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { AuthModule } from '@/modules/auth/auth.module';
import { ServicesModule } from '@shared/services/services.module';

// Entities
import { ChatConversation } from './entities/chat-conversation.entity';
import { ChatMessage } from './entities/chat-message.entity';
import { FacebookPageConfig } from './entities/facebook-page-config.entity';
import { ChatSession } from './entities/chat-session.entity';

// Controllers
import { ChatController } from './controllers/chat.controller';
import { FacebookWebhookController } from './controllers/facebook-webhook.controller';
import { ChatManagementController } from './controllers/chat-management.controller';

// Services
import { ChatService } from './services/chat.service';
import { FacebookService } from './services/facebook.service';
import { MessageProcessorService } from './services/message-processor.service';
import { AIOrchestatorService } from './services/ai-orchestrator.service';
import { RAGService } from './services/rag.service';
import { PromptManagerService } from './services/prompt-manager.service';

// Repositories
import { ChatConversationRepository } from './repositories/chat-conversation.repository';
import { ChatMessageRepository } from './repositories/chat-message.repository';
import { FacebookPageConfigRepository } from './repositories/facebook-page-config.repository';
import { ChatSessionRepository } from './repositories/chat-session.repository';

/**
 * Module quản lý chat tích hợp Facebook Page và AI
 */
@Module({
  imports: [
    AuthModule,
    ServicesModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    TypeOrmModule.forFeature([
      ChatConversation,
      ChatMessage,
      FacebookPageConfig,
      ChatSession,
    ]),
  ],
  controllers: [
    ChatController,
    FacebookWebhookController,
    ChatManagementController,
  ],
  providers: [
    // Repositories
    ChatConversationRepository,
    ChatMessageRepository,
    FacebookPageConfigRepository,
    ChatSessionRepository,

    // Services
    ChatService,
    FacebookService,
    MessageProcessorService,
    AIOrchestatorService,
    RAGService,
    PromptManagerService,
  ],
  exports: [
    ChatService,
    FacebookService,
    MessageProcessorService,
    AIOrchestatorService,
    RAGService,
  ],
})
export class ChatModule {}
