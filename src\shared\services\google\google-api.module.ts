import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GoogleApiService } from './google-api.service';
import { GoogleStorageService } from './google-storage.service';
import { GoogleVisionService } from './google-vision.service';
import { GoogleTranslateService } from './google-translate.service';
import { GoogleAdsService } from './google-ads.service';
import { GoogleAdsCampaignService } from './google-ads-campaign.service';
import { GoogleAdsKeywordService } from './google-ads-keyword.service';
import { GoogleAdsReportService } from './google-ads-report.service';

@Module({
  imports: [ConfigModule],
  providers: [
    GoogleApiService,
    GoogleStorageService,
    GoogleVisionService,
    GoogleTranslateService,
    GoogleAdsService,
    GoogleAdsCampaignService,
    GoogleAdsKeywordService,
    GoogleAdsReportService,
  ],
  exports: [
    GoogleApiService,
    GoogleStorageService,
    GoogleVisionService,
    GoogleTranslateService,
    GoogleAdsService,
    GoogleAdsCampaignService,
    GoogleAdsKeywordService,
    GoogleAdsReportService,
  ],
})
export class GoogleApiModule {}
