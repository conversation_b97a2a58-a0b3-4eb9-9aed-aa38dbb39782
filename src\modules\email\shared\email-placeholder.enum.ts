/**
 * Enum chứa các placeholder cho email chào mừng
 */
export enum WelcomeEmailPlaceholder {
  NAME = 'name',
  DATE = 'date',
}

/**
 * Enum chứa các placeholder cho email xác nhận đơn hàng
 */
export enum OrderConfirmationEmailPlaceholder {
  NAME = 'name',
  ORDER_ID = 'orderId',
  ORDER_DATE = 'orderDate',
  ITEMS = 'items',
  TOTAL_AMOUNT = 'totalAmount',
}

/**
 * Enum chứa các placeholder cho email thanh toán thành công
 */
export enum PaymentSuccessEmailPlaceholder {
  NAME = 'name',
  ORDER_ID = 'orderId',
  AMOUNT = 'amount',
  PAYMENT_METHOD = 'paymentMethod',
  PAYMENT_DATE = 'paymentDate',
}

/**
 * Enum chứa các placeholder cho email cập nhật trạng thái đơn hàng
 */
export enum OrderStatusUpdateEmailPlaceholder {
  NAME = 'name',
  ORDER_ID = 'orderId',
  STATUS = 'status',
  STATUS_DESCRIPTION = 'statusDescription',
  UPDATE_DATE = 'updateDate',
}

/**
 * Enum chứa các placeholder cho email xác thực OTP
 */
export enum VerifyOtpEmailPlaceholder {
  NAME = 'name',
  OTP_CODE = 'otpCode',
  EXPIRE_TIME = 'expireTime',
  APP_NAME = 'appName',
}

/**
 * Enum chứa các placeholder cho email quên mật khẩu
 */
export enum ForgotPasswordEmailPlaceholder {
  NAME = 'name',
  RESET_LINK = 'resetLink',
  EXPIRE_TIME = 'expireTime',
  APP_NAME = 'appName',
}

/**
 * Enum chứa các placeholder cho email xác thực 2 lớp
 */
export enum TwoFactorAuthEmailPlaceholder {
  NAME = 'name',
  AUTH_CODE = 'authCode',
  EXPIRE_TIME = 'expireTime',
  DEVICE_INFO = 'deviceInfo',
  IP_ADDRESS = 'ipAddress',
  LOCATION = 'location',
  TIME = 'time',
}

/**
 * Enum chứa các danh mục template email
 */
export enum EmailTemplateCategory {
  USER_WELCOME = 'user_welcome',
  ORDER_CONFIRMATION = 'order_confirmation',
  PAYMENT_SUCCESS = 'payment_success',
  ORDER_STATUS_UPDATE = 'order_status_update',
  VERIFY_OTP = 'verify_otp',
  FORGOT_PASSWORD = 'forgot_password',
  TWO_FACTOR_AUTH = 'two_factor_auth',
}
