import { Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
  RemoveEvent,
  FindOptions,
  FindOneOptions,
  SelectQueryBuilder,
} from 'typeorm';
import { InjectConnection } from '@nestjs/typeorm';
import { AsyncLocalStorage } from 'async_hooks';

/**
 * Interface cho entity có tenantId
 */
export interface TenantEntity {
  tenantId: number;
}

/**
 * AsyncLocalStorage để lưu trữ tenantId trong context hiện tại
 */
export const tenantContext = new AsyncLocalStorage<{
  tenantId: number;
  disableTenantFilter?: boolean; // Thêm flag để tắt tenant filtering
}>();

/**
 * Hàm để tạm thời tắt tenant filtering
 * Sử dụng hàm này khi cần truy cập dữ liệu của tất cả các tenant
 * @param callback Hàm callback cần thực thi mà không có tenant filtering
 * @returns Kết quả của hàm callback
 *
 * @example
 * // Sử dụng trong service
 * async findAllAcrossTenants(): Promise<User[]> {
 *   return withoutTenantFilter(async () => {
 *     return this.userRepository.findAll();
 *   });
 * }
 */
export function withoutTenantFilter<T>(callback: () => Promise<T>): Promise<T> {
  const currentStore = tenantContext.getStore();
  const tenantId = currentStore?.tenantId ?? -1; // Default to -1 if undefined

  // Tạo store mới với disableTenantFilter = true
  return tenantContext.run({ tenantId, disableTenantFilter: true }, callback);
}

/**
 * Subscriber để tự động thêm điều kiện tenantId vào các câu truy vấn
 * và đảm bảo tenantId không bị thay đổi trong quá trình cập nhật
 */
@Injectable()
export class TenantEntitySubscriber implements EntitySubscriberInterface {
  constructor(@InjectConnection() readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  /**
   * Kiểm tra xem entity có phải là TenantEntity không
   * @param entity Entity cần kiểm tra
   * @returns true nếu entity có trường tenantId
   */
  private isTenantEntity(entity: any): entity is TenantEntity {
    return entity && 'tenantId' in entity;
  }

  /**
   * Lấy tenantId từ context hiện tại
   * @returns tenantId hoặc undefined nếu không có hoặc đã tắt tenant filtering
   */
  private getCurrentTenantId(): number | undefined {
    const store = tenantContext.getStore();

    // Nếu disableTenantFilter = true, trả về undefined để không thêm điều kiện tenantId
    if (store?.disableTenantFilter) {
      return undefined;
    }

    return store?.tenantId;
  }

  /**
   * Được gọi trước khi entity được insert
   * @param event Event insert
   */
  beforeInsert(event: InsertEvent<any>): void {
    if (this.isTenantEntity(event.entity)) {
      const tenantId = this.getCurrentTenantId();

      // Chỉ set tenantId nếu chưa được set và có tenantId trong context
      if (tenantId !== undefined && event.entity.tenantId === undefined) {
        event.entity.tenantId = tenantId;
      }
    }
  }

  /**
   * Được gọi trước khi entity được update
   * @param event Event update
   */
  beforeUpdate(event: UpdateEvent<any>): void {
    // Ngăn chặn việc thay đổi tenantId
    if (
      event.entity &&
      this.isTenantEntity(event.entity) &&
      event.databaseEntity &&
      this.isTenantEntity(event.databaseEntity)
    ) {
      const newTenantId = event.entity.tenantId;
      const oldTenantId = event.databaseEntity.tenantId;

      // Nếu tenantId bị thay đổi, reset lại giá trị cũ
      if (newTenantId !== undefined && oldTenantId !== newTenantId) {
        event.entity.tenantId = oldTenantId;
      }
    }
  }

  /**
   * Được gọi trước khi entity được remove
   * @param event Event remove
   */
  beforeRemove(event: RemoveEvent<any>): void {
    // Đảm bảo chỉ xóa entity thuộc về tenant hiện tại
    if (this.isTenantEntity(event.entity) && event.entity.tenantId) {
      const currentTenantId = this.getCurrentTenantId();

      if (
        currentTenantId !== undefined &&
        event.entity.tenantId !== currentTenantId
      ) {
        // Phương thức thay thế vì TypeORM v4 không có stopPropagation
        event.entity = null; // Ngăn chặn việc xóa bằng cách đặt entity thành null
        event.queryRunner.release(); // Giải phóng queryRunner để ngăn tiếp tục xử lý
      }
    }
  }

  /**
   * Được gọi trước khi thực hiện câu truy vấn find
   * @param event Event find
   */
  beforeFind(event: any): void {
    this.addTenantIdToFindOptions(event);
  }

  /**
   * Được gọi trước khi thực hiện câu truy vấn findOne
   * @param event Event findOne
   */
  beforeFindOne(event: any): void {
    this.addTenantIdToFindOptions(event);
  }

  /**
   * Thêm điều kiện tenantId vào options của find và findOne
   * @param event Event find hoặc findOne
   */
  private addTenantIdToFindOptions(event: { entity: any; options: any }): void {
    const metadata = this.connection.getMetadata(event.entity);

    // Kiểm tra xem entity có trường tenantId không
    if (metadata.columns.some((column) => column.propertyName === 'tenantId')) {
      const tenantId = this.getCurrentTenantId();

      if (tenantId !== undefined) {
        // Khởi tạo where nếu chưa có
        if (!event.options.where) {
          event.options.where = {};
        }

        // Nếu where là object, thêm tenantId vào
        if (
          typeof event.options.where === 'object' &&
          !Array.isArray(event.options.where)
        ) {
          // Chỉ thêm tenantId nếu chưa được chỉ định
          if (event.options.where.tenantId === undefined) {
            event.options.where.tenantId = tenantId;
          }
        }
        // Nếu where là array, thêm tenantId vào mỗi phần tử
        else if (Array.isArray(event.options.where)) {
          event.options.where = event.options.where.map((criteria) => {
            if (
              typeof criteria === 'object' &&
              criteria.tenantId === undefined
            ) {
              return { ...criteria, tenantId };
            }
            return criteria;
          });
        }
      }
    }
  }

  /**
   * Được gọi trước khi thực hiện câu truy vấn với QueryBuilder
   * @param event Event query
   */
  beforeQuery(event: any): void {
    // Kiểm tra xem queryBuilder có tồn tại không
    if (!event.queryBuilder) {
      return;
    }

    const queryBuilder = event.queryBuilder as SelectQueryBuilder<any>;

    // Kiểm tra xem expressionMap và mainAlias có tồn tại không
    if (!queryBuilder.expressionMap || !queryBuilder.expressionMap.mainAlias) {
      return;
    }

    const metadata = queryBuilder.expressionMap.mainAlias?.metadata;

    // Kiểm tra xem metadata có tồn tại không
    if (!metadata) {
      return;
    }

    // Kiểm tra xem entity có trường tenantId không
    if (metadata.columns.some((column) => column.propertyName === 'tenantId')) {
      const tenantId = this.getCurrentTenantId();

      if (tenantId !== undefined) {
        const alias = queryBuilder.expressionMap.mainAlias?.name;

        // Kiểm tra xem alias có tồn tại không
        if (!alias) {
          return;
        }

        // Kiểm tra xem đã có điều kiện tenantId chưa
        const hasTenantCondition = queryBuilder.expressionMap.wheres?.some(
          (where) => {
            if (where.condition && typeof where.condition === 'string') {
              return where.condition.indexOf(`${alias}.tenantId =`) >= 0;
            }
            return false;
          },
        );

        // Nếu chưa có điều kiện tenantId, thêm vào
        if (!hasTenantCondition) {
          queryBuilder.andWhere(`${alias}.tenantId = :tenantId`, { tenantId });
        }
      }
    }
  }
}
