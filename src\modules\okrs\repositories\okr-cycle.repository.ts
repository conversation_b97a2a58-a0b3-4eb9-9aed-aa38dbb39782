import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OkrCycle } from '../entities/okr-cycle.entity';
import { OkrCycleStatus } from '../enum/okr-cycle-status.enum';
import { OkrCycleQueryDto } from '../dto/okr-cycle/okr-cycle-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository for OKR cycles
 */
@Injectable()
export class OkrCycleRepository {
  constructor(
    @InjectRepository(OkrCycle)
    private readonly repository: Repository<OkrCycle>,
  ) {}

  /**
   * Find all OKR cycles with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of OKR cycles
   */
  async findAll(query: OkrCycleQueryDto): Promise<PaginatedResult<OkrCycle>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'startDate',
      sortDirection = 'DESC',
      status,
      startDate,
      endDate,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('cycle');

    // Apply status filter if provided
    if (status) {
      queryBuilder.andWhere('cycle.status = :status', { status });
    }

    // Apply startDate filter if provided
    if (startDate) {
      queryBuilder.andWhere('cycle.startDate >= :startDate', { startDate });
    }

    // Apply endDate filter if provided
    if (endDate) {
      queryBuilder.andWhere('cycle.endDate <= :endDate', { endDate });
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('cycle.name ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`cycle.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find an OKR cycle by ID
   * @param id OKR cycle ID
   * @returns OKR cycle or null if not found
   */
  async findById(id: number): Promise<OkrCycle | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Create a new OKR cycle
   * @param data OKR cycle data
   * @returns Created OKR cycle
   */
  async create(data: Partial<OkrCycle>): Promise<OkrCycle> {
    const cycle = this.repository.create(data);
    return this.repository.save(cycle);
  }

  /**
   * Update an OKR cycle
   * @param id OKR cycle ID
   * @param data Updated OKR cycle data
   * @returns Updated OKR cycle or null if not found
   */
  async update(id: number, data: Partial<OkrCycle>): Promise<OkrCycle | null> {
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  /**
   * Delete an OKR cycle
   * @param id OKR cycle ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete({ id });
    return (
      result.affected !== undefined &&
      result.affected !== null &&
      result.affected > 0
    );
  }

  /**
   * Find the active OKR cycle
   * @returns Active OKR cycle or null if not found
   */
  async findActive(): Promise<OkrCycle | null> {
    return this.repository.findOne({
      where: { status: OkrCycleStatus.ACTIVE },
    });
  }
}
