import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Project } from '../entities/project.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { ProjectQueryDto } from '../dto/project/project-query.dto';

/**
 * Repository cho entity Project
 */
@Injectable()
export class ProjectRepository {
  constructor(
    @InjectRepository(Project)
    private readonly repository: Repository<Project>,
  ) {}

  /**
   * Tạo dự án mới
   * @param data Dữ liệu dự án
   * @returns Dự án đã tạo
   */
  async create(data: Partial<Project>): Promise<Project> {
    const project = this.repository.create(data);
    return this.repository.save(project);
  }

  /**
   * Tìm tất cả dự án với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách dự án đã phân trang
   */
  async findAll(query: ProjectQueryDto): Promise<PaginatedResult<Project>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      isActive,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('project');

    // Áp dụng bộ lọc isActive nếu được cung cấp
    if (isActive !== undefined) {
      queryBuilder.andWhere('project.isActive = :isActive', { isActive });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'project.title ILIKE :search OR project.description ILIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`project.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm dự án theo ID
   * @param id ID dự án
   * @returns Dự án hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<Project | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm dự án theo chủ sở hữu
   * @param ownerId ID chủ sở hữu
   * @returns Danh sách dự án
   */
  async findByOwnerId(ownerId: number): Promise<Project[]> {
    return this.repository.find({
      where: { ownerId },
    });
  }

  /**
   * Cập nhật dự án
   * @param id ID dự án
   * @param data Dữ liệu cập nhật
   * @returns Dự án đã cập nhật
   */
  async update(id: number, data: Partial<Project>): Promise<Project | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa dự án
   * @param id ID dự án
   * @returns Kết quả xóa
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }
}
