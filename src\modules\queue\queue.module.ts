import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';
import { QueueName, DEFAULT_JOB_OPTIONS } from './queue.constants';
import { QueueTestController } from './controllers/queue-test.controller';
import { EmailProcessor } from './processors/email.processor';

/**
 * Module quản lý hệ thống queue của ứng dụng
 */
@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URL');

        return {
          // Cấu hình kết nối Redis dựa trên URL thay vì các tham số riêng lẻ
          redis: redisUrl,
          defaultJobOptions: DEFAULT_JOB_OPTIONS,
        };
      },
    }),
    /**
     * Đăng ký các queue cụ thể ở đây
     * Mỗi queue là một module con trong hệ thống
     */
    BullModule.registerQueue(
      {
        name: QueueName.EMAIL, // Queue xử lý email
      },
      {
        name: QueueName.SMS, // Queue xử lý SMS
      },
      {
        name: QueueName.NOTIFICATION, // Queue xử lý thông báo
      },
      {
        name: QueueName.DATA_PROCESS, // Queue xử lý dữ liệu
      },
      {
        name: QueueName.TODO, // Queue xử lý công việc
      },
      {
        name: QueueName.PROJECT, // Queue xử lý dự án
      },
      {
        name: QueueName.REPORT, // Queue xử lý báo cáo
      },
      {
        name: QueueName.EXPORT, // Queue xử lý xuất dữ liệu
      },
      {
        name: QueueName.IMPORT, // Queue xử lý nhập dữ liệu
      },
      {
        name: QueueName.SYNC, // Queue xử lý đồng bộ dữ liệu
      },
    ),
  ],
  controllers: [QueueTestController],
  providers: [QueueService, EmailProcessor],
  exports: [QueueService, BullModule],
})
export class QueueModule {}
