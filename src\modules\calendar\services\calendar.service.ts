import { Injectable, Logger } from '@nestjs/common';
import { CalendarRepository } from '../repositories/calendar.repository';
import {
  CalendarEventDto,
  CreateCalendarEventDto,
  UpdateCalendarEventDto,
} from '../dto/calendar-event.dto';
import { CalendarQueryDto } from '../dto/calendar-query.dto';
import { ReferenceType } from '../enum/reference-type.enum';
import { AppException } from '@/common/exceptions/app.exception';
import { CALENDAR_ERROR_CODES } from '../errors/calendar-error.code';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { Calendar } from '../entities/calendar.entity';

/**
 * Service cho Calendar
 */
@Injectable()
export class CalendarService {
  private readonly logger = new Logger(CalendarService.name);

  constructor(
    private readonly calendarRepository: CalendarRepository,
    // Inject các service khác nếu cần
    // private readonly todoService: TodoService,
    // private readonly leaveRequestService: LeaveRequestService,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> tất cả sự kiện lịch trong khoảng thời gian
   * @param query Tham số truy vấn
   * @param currentUserId ID của người dùng hiện tại
   * @returns Danh sách sự kiện lịch
   */
  async getCalendarEvents(
    query: CalendarQueryDto,
    currentUserId: number,
  ): Promise<PaginatedResult<CalendarEventDto>> {
    try {
      // Kiểm tra khoảng thời gian hợp lệ
      if (query.startDate >= query.endDate) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_INVALID_DATE_RANGE,
          'Thời gian bắt đầu phải trước thời gian kết thúc',
        );
      }

      // Lấy các sự kiện từ bảng calendars
      const result = await this.calendarRepository.findAll(query);

      // Chuyển đổi các sự kiện từ entity sang DTO
      const items = await this.mapCalendarEventsToDto(
        result.items,
        currentUserId,
      );

      // TODO: Bổ sung dữ liệu từ các nguồn khác (todo, leave_request, v.v.)
      // Ví dụ:
      // const todoEvents = await this.getTodoEvents(query.startDate, query.endDate, currentUserId);
      // const leaveRequestEvents = await this.getLeaveRequestEvents(query.startDate, query.endDate, currentUserId);
      // items.push(...todoEvents, ...leaveRequestEvents);

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting calendar events: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CALENDAR_ERROR_CODES.CALENDAR_EVENT_NOT_FOUND,
        `Không thể lấy danh sách sự kiện lịch: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sự kiện lịch
   * @param id ID của sự kiện lịch
   * @param currentUserId ID của người dùng hiện tại
   * @returns Chi tiết sự kiện lịch
   */
  async getCalendarEvent(
    id: number,
    currentUserId: number,
  ): Promise<CalendarEventDto> {
    try {
      const event = await this.calendarRepository.findById(id);
      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_EVENT_NOT_FOUND,
          `Không tìm thấy sự kiện lịch với ID ${id}`,
        );
      }

      return this.mapCalendarEventToDto(event, currentUserId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting calendar event: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CALENDAR_ERROR_CODES.CALENDAR_EVENT_NOT_FOUND,
        `Không thể lấy chi tiết sự kiện lịch: ${error.message}`,
      );
    }
  }

  /**
   * Tạo sự kiện lịch mới
   * @param createDto Dữ liệu tạo sự kiện lịch
   * @param currentUserId ID của người dùng hiện tại
   * @returns Sự kiện lịch đã tạo
   */
  async createCalendarEvent(
    createDto: CreateCalendarEventDto,
    currentUserId: number,
  ): Promise<CalendarEventDto> {
    try {
      // Kiểm tra khoảng thời gian hợp lệ
      if (createDto.endTime && createDto.startTime >= createDto.endTime) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_INVALID_DATE_RANGE,
          'Thời gian bắt đầu phải trước thời gian kết thúc',
        );
      }

      const now = Date.now();
      const event = await this.calendarRepository.create({
        startTime: createDto.startTime,
        endTime: createDto.endTime || null,
        description: createDto.description || null,
        info: {
          title: createDto.title,
          ...createDto.info,
        },
        referenceType: createDto.referenceType || ReferenceType.CALENDAR,
        referenceId: createDto.referenceId || null,
        createdAt: now,
        updatedAt: now,
        updatedBy: currentUserId,
      });

      return this.mapCalendarEventToDto(event, currentUserId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating calendar event: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CALENDAR_ERROR_CODES.CALENDAR_EVENT_CREATE_FAILED,
        `Không thể tạo sự kiện lịch: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sự kiện lịch
   * @param id ID của sự kiện lịch
   * @param updateDto Dữ liệu cập nhật sự kiện lịch
   * @param currentUserId ID của người dùng hiện tại
   * @returns Sự kiện lịch đã cập nhật
   */
  async updateCalendarEvent(
    id: number,
    updateDto: UpdateCalendarEventDto,
    currentUserId: number,
  ): Promise<CalendarEventDto> {
    try {
      const event = await this.calendarRepository.findById(id);
      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_EVENT_NOT_FOUND,
          `Không tìm thấy sự kiện lịch với ID ${id}`,
        );
      }

      // Kiểm tra khoảng thời gian hợp lệ
      if (updateDto.endTime && updateDto.startTime >= updateDto.endTime) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_INVALID_DATE_RANGE,
          'Thời gian bắt đầu phải trước thời gian kết thúc',
        );
      }

      // Cập nhật thông tin
      const updatedEvent = await this.calendarRepository.update(id, {
        startTime: updateDto.startTime,
        endTime: updateDto.endTime || event.endTime,
        description:
          updateDto.description !== undefined
            ? updateDto.description
            : event.description,
        info: updateDto.info
          ? {
              ...event.info,
              ...updateDto.info,
              title: updateDto.title || event.info?.title,
            }
          : { ...event.info, title: updateDto.title || event.info?.title },
        referenceType: updateDto.referenceType || event.referenceType,
        referenceId:
          updateDto.referenceId !== undefined
            ? updateDto.referenceId
            : event.referenceId,
        updatedAt: Date.now(),
        updatedBy: currentUserId,
      });

      return this.mapCalendarEventToDto(updatedEvent, currentUserId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating calendar event: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CALENDAR_ERROR_CODES.CALENDAR_EVENT_UPDATE_FAILED,
        `Không thể cập nhật sự kiện lịch: ${error.message}`,
      );
    }
  }

  /**
   * Xóa sự kiện lịch
   * @param id ID của sự kiện lịch
   * @param currentUserId ID của người dùng hiện tại
   * @returns true nếu xóa thành công
   */
  async deleteCalendarEvent(
    id: number,
    currentUserId: number,
  ): Promise<boolean> {
    try {
      const event = await this.calendarRepository.findById(id);
      if (!event) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_EVENT_NOT_FOUND,
          `Không tìm thấy sự kiện lịch với ID ${id}`,
        );
      }

      // Chỉ cho phép xóa sự kiện lịch thông thường, không xóa sự kiện tham chiếu
      if (event.referenceType !== ReferenceType.CALENDAR && event.referenceId) {
        throw new AppException(
          CALENDAR_ERROR_CODES.CALENDAR_PERMISSION_DENIED,
          'Không thể xóa sự kiện tham chiếu trực tiếp',
        );
      }

      const result = await this.calendarRepository.delete(id);
      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error deleting calendar event: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CALENDAR_ERROR_CODES.CALENDAR_EVENT_DELETE_FAILED,
        `Không thể xóa sự kiện lịch: ${error.message}`,
      );
    }
  }

  /**
   * Chuyển đổi entity Calendar sang DTO
   * @param event Entity Calendar
   * @param currentUserId ID của người dùng hiện tại
   * @returns CalendarEventDto
   */
  private async mapCalendarEventToDto(
    event: Calendar,
    currentUserId: number,
  ): Promise<CalendarEventDto> {
    const info = event.info || {};

    // Xác định màu và icon dựa trên loại sự kiện
    const { color, icon } = this.getEventStyle(event.referenceType);

    return {
      id: event.id,
      title: info.title || 'Sự kiện không có tiêu đề',
      startTime: event.startTime,
      endTime: event.endTime,
      description: event.description,
      type: (event.referenceType as ReferenceType) || ReferenceType.CALENDAR,
      referenceId: event.referenceId,
      color: info.color || color,
      icon: info.icon || icon,
      additionalInfo: info,
      editable: event.referenceType === ReferenceType.CALENDAR,
    };
  }

  /**
   * Chuyển đổi danh sách entity Calendar sang DTO
   * @param events Danh sách entity Calendar
   * @param currentUserId ID của người dùng hiện tại
   * @returns Danh sách CalendarEventDto
   */
  private async mapCalendarEventsToDto(
    events: Calendar[],
    currentUserId: number,
  ): Promise<CalendarEventDto[]> {
    const dtos: CalendarEventDto[] = [];

    for (const event of events) {
      const dto = await this.mapCalendarEventToDto(event, currentUserId);
      dtos.push(dto);
    }

    return dtos;
  }

  /**
   * Lấy style (màu, icon) cho loại sự kiện
   * @param type Loại sự kiện
   * @returns Màu và icon
   */
  private getEventStyle(type: string | null): { color: string; icon: string } {
    switch (type) {
      case ReferenceType.TODO:
        return { color: '#FF5733', icon: 'task' };
      case ReferenceType.LEAVE_REQUEST:
        return { color: '#33FF57', icon: 'event_busy' };
      case ReferenceType.MEETING:
        return { color: '#3366FF', icon: 'groups' };
      case ReferenceType.PROJECT_DEADLINE:
        return { color: '#FF3366', icon: 'alarm' };
      case ReferenceType.COMPANY_EVENT:
        return { color: '#9933FF', icon: 'celebration' };
      case ReferenceType.BIRTHDAY:
        return { color: '#FF99CC', icon: 'cake' };
      case ReferenceType.WORK_ANNIVERSARY:
        return { color: '#FFCC33', icon: 'work' };
      case ReferenceType.HOLIDAY:
        return { color: '#33CCFF', icon: 'beach_access' };
      default:
        return { color: '#3788d8', icon: 'event' };
    }
  }

  // TODO: Triển khai các phương thức để lấy dữ liệu từ các nguồn khác
  // Ví dụ:
  // private async getTodoEvents(startDate: number, endDate: number, userId: number): Promise<CalendarEventDto[]> { ... }
  // private async getLeaveRequestEvents(startDate: number, endDate: number, userId: number): Promise<CalendarEventDto[]> { ... }
}
