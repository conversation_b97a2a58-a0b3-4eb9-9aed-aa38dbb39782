import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Interface cho Facebook Page Access Token
 */
interface FacebookPageToken {
  pageId: string;
  accessToken: string;
  expiresAt?: number;
}

/**
 * Interface cho Facebook User Profile
 */
interface FacebookUserProfile {
  id: string;
  name: string;
  firstName?: string;
  lastName?: string;
  profilePic?: string;
}

/**
 * Interface cho Facebook Message
 */
interface FacebookMessage {
  recipient: { id: string };
  message: {
    text?: string;
    attachment?: any;
    quick_replies?: any[];
  };
  messaging_type?: string;
}

/**
 * Service xử lý tích hợp với Facebook Messenger API
 */
@Injectable()
export class FacebookService {
  private readonly logger = new Logger(FacebookService.name);
  private readonly graphApiUrl = 'https://graph.facebook.com/v18.0';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn text đến Facebook user
   */
  async sendTextMessage(
    pageAccessToken: string,
    recipientId: string,
    text: string,
  ): Promise<any> {
    try {
      const message: FacebookMessage = {
        recipient: { id: recipientId },
        message: { text },
        messaging_type: 'RESPONSE',
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.graphApiUrl}/me/messages`,
          message,
          {
            params: { access_token: pageAccessToken },
            headers: { 'Content-Type': 'application/json' },
          },
        ),
      );

      this.logger.log(`Sent message to ${recipientId}: ${text}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send message: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to send Facebook message: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn với quick replies
   */
  async sendQuickReplies(
    pageAccessToken: string,
    recipientId: string,
    text: string,
    quickReplies: Array<{ title: string; payload: string }>,
  ): Promise<any> {
    try {
      const message: FacebookMessage = {
        recipient: { id: recipientId },
        message: {
          text,
          quick_replies: quickReplies.map(reply => ({
            content_type: 'text',
            title: reply.title,
            payload: reply.payload,
          })),
        },
        messaging_type: 'RESPONSE',
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.graphApiUrl}/me/messages`,
          message,
          {
            params: { access_token: pageAccessToken },
            headers: { 'Content-Type': 'application/json' },
          },
        ),
      );

      this.logger.log(`Sent quick replies to ${recipientId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send quick replies: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to send Facebook quick replies: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin profile của Facebook user
   */
  async getUserProfile(
    pageAccessToken: string,
    userId: string,
  ): Promise<FacebookUserProfile> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.graphApiUrl}/${userId}`,
          {
            params: {
              access_token: pageAccessToken,
              fields: 'id,name,first_name,last_name,profile_pic',
            },
          },
        ),
      );

      return {
        id: response.data.id,
        name: response.data.name,
        firstName: response.data.first_name,
        lastName: response.data.last_name,
        profilePic: response.data.profile_pic,
      };
    } catch (error) {
      this.logger.error(`Failed to get user profile: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to get Facebook user profile: ${error.message}`,
      );
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(
    payload: string,
    signature: string,
    appSecret: string,
  ): boolean {
    try {
      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha1', appSecret)
        .update(payload)
        .digest('hex');
      
      return signature === `sha1=${expectedSignature}`;
    } catch (error) {
      this.logger.error(`Failed to verify webhook signature: ${error.message}`);
      return false;
    }
  }

  /**
   * Set typing indicator
   */
  async setTypingIndicator(
    pageAccessToken: string,
    recipientId: string,
    action: 'typing_on' | 'typing_off',
  ): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.post(
          `${this.graphApiUrl}/me/messages`,
          {
            recipient: { id: recipientId },
            sender_action: action,
          },
          {
            params: { access_token: pageAccessToken },
            headers: { 'Content-Type': 'application/json' },
          },
        ),
      );
    } catch (error) {
      this.logger.error(`Failed to set typing indicator: ${error.message}`);
    }
  }
}
