import { Injectable, Logger } from '@nestjs/common';
import { AdminTemplateEmailRepository } from '../repositories/admin-template-email.repository';
import {
  CreateAdminTemplateEmailDto,
  UpdateAdminTemplateEmailDto,
  SendTemplateEmailDto,
} from '../dto/admin-template-email.dto';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { TemplateEmailService } from '@shared/services/template-email';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Service xử lý các thao tác với template email
 */
@Injectable()
export class AdminTemplateEmailService {
  private readonly logger = new Logger(AdminTemplateEmailService.name);

  constructor(
    private readonly adminTemplateEmailRepository: AdminTemplateEmailRepository,
    private readonly templateEmailService: TemplateEmailService,
  ) {}

  /**
   * L<PERSON>y danh sách tất cả template email
   * @returns Danh sách template email
   */
  async findAll(): Promise<AdminTemplateEmail[]> {
    return this.adminTemplateEmailRepository.findAll();
  }

  /**
   * Lấy danh sách tất cả template email không bao gồm nội dung
   * @returns Danh sách template email không bao gồm nội dung
   */
  async findAllWithoutContent(): Promise<Partial<AdminTemplateEmail>[]> {
    return this.adminTemplateEmailRepository.findAllWithoutContent();
  }

  /**
   * Tìm template email theo ID
   * @param id ID của template
   * @returns Template email
   * @throws AppException nếu không tìm thấy template
   */
  async findById(id: number): Promise<AdminTemplateEmail> {
    const template = await this.adminTemplateEmailRepository.findById(id);
    if (!template) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        `Không tìm thấy mẫu email với ID ${id}`,
      );
    }
    return template;
  }

  /**
   * Tìm template email theo danh mục
   * @param category Danh mục của template
   * @returns Template email
   * @throws AppException nếu không tìm thấy template
   */
  async findByCategory(category: string): Promise<AdminTemplateEmail> {
    const template =
      await this.adminTemplateEmailRepository.findByCategory(category);
    if (!template) {
      throw new AppException(
        ErrorCode.RESOURCE_NOT_FOUND,
        `Không tìm thấy mẫu email với danh mục ${category}`,
      );
    }
    return template;
  }

  /**
   * Tạo mới template email
   * @param createDto Dữ liệu tạo mới
   * @returns Template email đã tạo
   */
  async create(
    createDto: CreateAdminTemplateEmailDto,
  ): Promise<AdminTemplateEmail> {
    // Kiểm tra xem danh mục đã tồn tại chưa
    const existingTemplate =
      await this.adminTemplateEmailRepository.findByCategory(
        createDto.category,
      );
    if (existingTemplate) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Mẫu email với danh mục ${createDto.category} đã tồn tại`,
      );
    }

    return this.adminTemplateEmailRepository.create(createDto);
  }

  /**
   * Cập nhật template email
   * @param id ID của template cần cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns Template email đã cập nhật
   */
  async update(
    id: number,
    updateDto: UpdateAdminTemplateEmailDto,
  ): Promise<AdminTemplateEmail> {
    // Kiểm tra xem template có tồn tại không
    await this.findById(id);

    // Kiểm tra xem danh mục mới đã tồn tại chưa (nếu có thay đổi danh mục)
    if (updateDto.category) {
      const existingTemplate =
        await this.adminTemplateEmailRepository.findByCategory(
          updateDto.category,
        );
      if (existingTemplate && existingTemplate.id !== id) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Mẫu email với danh mục ${updateDto.category} đã tồn tại`,
        );
      }
    }

    return this.adminTemplateEmailRepository.update(id, updateDto);
  }

  /**
   * Xóa template email
   * @param id ID của template cần xóa
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    // Kiểm tra xem template có tồn tại không
    await this.findById(id);

    return this.adminTemplateEmailRepository.delete(id);
  }

  /**
   * Gửi email sử dụng template
   * @param sendDto Dữ liệu gửi email
   * @returns true nếu gửi thành công
   */
  async sendTemplateEmail(sendDto: SendTemplateEmailDto): Promise<boolean> {
    try {
      // Tìm template theo danh mục
      const template = await this.findByCategory(sendDto.category);

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: sendDto.to,
        data: sendDto.data,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error sending template email: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email',
      );
    }
  }
}
