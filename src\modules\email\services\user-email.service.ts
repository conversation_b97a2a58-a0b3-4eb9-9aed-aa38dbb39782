import { Injectable, Logger } from '@nestjs/common';
import { AdminTemplateEmailService } from './admin-template-email.service';
import { TemplateEmailService } from '@shared/services/template-email';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Service xử lý gửi email cho người dùng
 */
@Injectable()
export class UserEmailService {
  private readonly logger = new Logger(UserEmailService.name);

  constructor(
    private readonly adminTemplateEmailService: AdminTemplateEmailService,
    private readonly templateEmailService: TemplateEmailService,
  ) {}

  /**
   * Gửi email chào mừng cho người dùng mới
   * @param email Email người dùng
   * @param name Tên người dùng
   * @returns true nếu gửi thành công
   */
  async sendWelcomeEmail(email: string, name: string): Promise<boolean> {
    try {
      // L<PERSON>y template từ database
      const template =
        await this.adminTemplateEmailService.findByCategory('user_welcome');

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: {
          name,
          date: new Date().toLocaleDateString('vi-VN'),
        },
      });
    } catch (error) {
      this.logger.error(
        `Error sending welcome email: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email chào mừng',
      );
    }
  }

  /**
   * Gửi email thông báo đơn hàng mới
   * @param email Email người dùng
   * @param name Tên người dùng
   * @param orderId Mã đơn hàng
   * @param orderItems Danh sách sản phẩm
   * @param totalAmount Tổng tiền
   * @returns true nếu gửi thành công
   */
  async sendNewOrderEmail(
    email: string,
    name: string,
    orderId: string,
    orderItems: Array<{ name: string; quantity: number; price: number }>,
    totalAmount: number,
  ): Promise<boolean> {
    try {
      // Tạo HTML cho danh sách sản phẩm
      const itemsHtml = orderItems
        .map(
          (item) =>
            `<tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>${item.price.toLocaleString('vi-VN')} đ</td>
              <td>${(item.quantity * item.price).toLocaleString('vi-VN')} đ</td>
            </tr>`,
        )
        .join('');

      // Lấy template từ database
      const template =
        await this.adminTemplateEmailService.findByCategory(
          'order_confirmation',
        );

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: {
          name,
          orderId,
          orderDate: new Date().toLocaleDateString('vi-VN'),
          items: itemsHtml,
          totalAmount: totalAmount.toLocaleString('vi-VN'),
        },
      });
    } catch (error) {
      this.logger.error(
        `Error sending order confirmation email: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email xác nhận đơn hàng',
      );
    }
  }

  /**
   * Gửi email thông báo thanh toán thành công
   * @param email Email người dùng
   * @param name Tên người dùng
   * @param orderId Mã đơn hàng
   * @param amount Số tiền đã thanh toán
   * @param paymentMethod Phương thức thanh toán
   * @returns true nếu gửi thành công
   */
  async sendPaymentSuccessEmail(
    email: string,
    name: string,
    orderId: string,
    amount: number,
    paymentMethod: string,
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template =
        await this.adminTemplateEmailService.findByCategory('payment_success');

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: {
          name,
          orderId,
          amount: amount.toLocaleString('vi-VN'),
          paymentMethod,
          paymentDate: new Date().toLocaleDateString('vi-VN'),
        },
      });
    } catch (error) {
      this.logger.error(
        `Error sending payment success email: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email xác nhận thanh toán',
      );
    }
  }

  /**
   * Gửi email thông báo cập nhật trạng thái đơn hàng
   * @param email Email người dùng
   * @param name Tên người dùng
   * @param orderId Mã đơn hàng
   * @param status Trạng thái mới
   * @param statusDescription Mô tả trạng thái
   * @returns true nếu gửi thành công
   */
  async sendOrderStatusUpdateEmail(
    email: string,
    name: string,
    orderId: string,
    status: string,
    statusDescription: string,
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(
        'order_status_update',
      );

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: {
          name,
          orderId,
          status,
          statusDescription,
          updateDate: new Date().toLocaleDateString('vi-VN'),
        },
      });
    } catch (error) {
      this.logger.error(
        `Error sending order status update email: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email cập nhật trạng thái đơn hàng',
      );
    }
  }
}
